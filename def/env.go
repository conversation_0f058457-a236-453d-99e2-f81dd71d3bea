package def

import (
	"os"
)

var (
	K8S_Node      = os.Getenv("K8S_NODE") //k8s节点名称
	K8S_SPACENAME = os.Getenv("K8S_SPACENAME")
	K8S_ENABLE    = len(K8S_Node) > 0 //是否是k8s环境

	KAFKA_BROKERS = os.Getenv("kafka_brokers")

	REDIS_ADDRESS         = os.Getenv("REDIS_ADDRESS")
	REDIS_CLUSTER_ADDRESS = os.Getenv("REDIS_CLUSTER_ADDRESS")
	REDIS_CLUSTER_ENABLE  = len(REDIS_CLUSTER_ADDRESS) > 0

	MQTT_BROKERS  = os.Getenv("MQTT_BROKERS")
	MQTT_CLIENTID = os.Getenv("MQTT_CLIENTID")
	MQTT_USER     = os.Getenv("MQTT_USER")
	MQTT_PASSWORD = os.Getenv("MQTT_PASSWORD")
	MQTT_ROOT_CA  = os.Getenv("MQTT_ROOT_CA")
	MQTT_CERT     = os.Getenv("MQTT_CERT")
	MQTT_PRIV_KEY = os.Getenv("MQTT_PRIV_KEY")

	QOS = os.Getenv("QOS")

	HUB_CLUSTER_ENABLE = os.Getenv("HUB_CLUSTER_ENABLE") == "enable" //hub是否分布式
)
