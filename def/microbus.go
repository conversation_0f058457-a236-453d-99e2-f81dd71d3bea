package def

import (
	microbus "foxess.cloud/fmicrobus"
	"os"
)

const (
	HubGroup          = "hub"
	HubUIResource     = `$system/device/setting/ui/%s/c/v0/json`
	HubGetResource    = `$system/device/setting/get/%s/c/v0/json`
	HubSetResource    = `$system/device/setting/set/%s/c/v0/json`
	HubGetRawResource = `$system/device/setting/rawcmd/%s/c/v0/json`
)

var Micbus *microbus.MicroBus

func init() {

	if K8S_ENABLE {
		KAFKA_BROKERS = os.Getenv("KAFKA_BROKERS")
	}
	Micbus = microbus.New(KAFKA_BROKERS)
}
