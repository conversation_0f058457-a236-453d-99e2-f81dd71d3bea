package redis

import (
	"context"
	"foxess.cloud/toolbox/flog"
	"foxess.cockscomb/def"
	"github.com/go-redis/redis/v8"
	"log"
	"os"
	"strings"
	"time"
)

const (
	DEVICE_INDEX_HUB_KEY = "device:index:hub:hash"
	MODULE_INDEX_HUB_KEY = "module:index:hub:hash"
)

// redis
var rsdb *redis.Client = nil        //单机redis
var rcdb *redis.ClusterClient = nil //集群版本redis
var REDIS_ENABLE = len(def.REDIS_CLUSTER_ADDRESS) > 0 || len(def.REDIS_ADDRESS) > 0

func Init() {
	if def.REDIS_CLUSTER_ENABLE { //使用集群
		rcdb = redis.NewClusterClient(&redis.ClusterOptions{
			Addrs:        strings.Split(def.REDIS_CLUSTER_ADDRESS, ";"),
			PoolSize:     128,
			MinIdleConns: 32,
			Password:     os.Getenv("REDIS_CLUSTER_PASSWORD"),
		})
		if rcdb == nil {
			flog.Log.Fatal("invalid redis clinet addr:", def.REDIS_CLUSTER_ADDRESS)
			os.Exit(1)
		} else {
			flog.Log.Infoln("redis cluster connect success addr:", def.REDIS_CLUSTER_ADDRESS)
		}
	} else {
		rsdb = redis.NewClient(&redis.Options{
			Addr:         def.REDIS_ADDRESS,
			PoolSize:     128,
			MinIdleConns: 32,
		})
		if rsdb == nil {
			flog.Log.Fatal("invalid redis clinet addr:", def.REDIS_ADDRESS)
			os.Exit(1)
		} else {
			flog.Log.Infoln("redis connect success addr:", def.REDIS_ADDRESS)
		}
	}

}

func GetDevHubIndex(sn string) string {
	if REDIS_ENABLE {
		ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
		_ = cancel
		var cmd *redis.StringCmd
		if def.REDIS_CLUSTER_ENABLE {
			cmd = rcdb.HGet(ctx, DEVICE_INDEX_HUB_KEY, sn)
		} else {
			cmd = rsdb.HGet(ctx, DEVICE_INDEX_HUB_KEY, sn)
		}
		c, e := cmd.Result()
		flog.Log.Infof("redis: %s: result: %s err: %v\n", sn, c, e)
		if e != nil || len(c) == 0 {
			return "0"
		}

		return c
	}
	return "0"
}

func GetModHubIndex(sn string) string {
	if REDIS_ENABLE {
		ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
		_ = cancel
		var cmd *redis.StringCmd
		if def.REDIS_CLUSTER_ENABLE {
			cmd = rcdb.HGet(ctx, MODULE_INDEX_HUB_KEY, sn)
		} else {
			cmd = rsdb.HGet(ctx, MODULE_INDEX_HUB_KEY, sn)
		}
		c, e := cmd.Result()
		if e != nil || len(c) == 0 {
			log.Println()
			return "0"
		}
		return c
	}
	return "0"
}
