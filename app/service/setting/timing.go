package setting

import (
	"encoding/json"
	"fmt"
	"foxess.cloud/toolbox/flog"
	"foxess.cockscomb/app/service/hub"
	"foxess.cockscomb/def"
	mqtt "github.com/eclipse/paho.mqtt.golang"
	"strings"
	"time"
)

// topic
const (
	//订阅
	remoteTimingReqTopic = "foxess/device/timing/+/v0/+/req" //第一个+:set/get   第二个+:devSN

	//发布
	remoteSetTimingRespTopic = "foxess/device/timing/%s/v0/%s/resp"
)

// timing 设置块名和字段名
const (
	timingBlock = "system_time"
)

// SetTiming 校时
func SetTiming(c mqtt.Client) {
	token := c.Subscribe(remoteTimingReqTopic, 0, handleRequest)
	go func() {
		if !token.WaitTimeout(5 * time.Second) {
			flog.Log.Errorf("subscribe timeout 5s")
			return
		}
	}()
}

func handleRequest(c mqtt.Client, msg mqtt.Message) {
	resource := msg.Topic()
	payload := msg.Payload()

	flog.Log.Infof("subscribe %s success:%s", resource, string(payload))

	rscs := strings.Split(resource, "/")
	if len(rscs) != 7 {
		flog.Log.Errorln("invalid topic:", resource)
		return
	}

	devSN := rscs[5]

	switch rscs[3] {
	case "set":
		var sta *setTimingArgs
		err := json.Unmarshal(payload, &sta)
		if err != nil {
			flog.Log.Errorf("error: %s json.Unmarshal fail, payload:%s", devSN, string(payload))
			return
		}

		handleSetTiming(c, devSN, sta)
	case "get":
		var gta *getTimingArgs
		err := json.Unmarshal(payload, &gta)
		if err != nil {
			flog.Log.Errorf("error: %s json.Unmarshal fail, payload:%s", devSN, string(payload))
			return
		}
		handleGetTiming(c, devSN, gta)
	}
}

type setTimingArgs struct {
	ID     uint32            `json:"id"`
	Values map[string]string `json:"values"`
}

type setTimingResp struct {
	Id    uint32 `json:"id"`
	Errno int    `json:"errno"`
}

func handleSetTiming(c mqtt.Client, devSN string, payload *setTimingArgs) {
	//下发设置并返回推送消息
	var str = &setTimingResp{
		Id:    payload.ID,
		Errno: 0,
	}

	if payload == nil || payload.Values == nil {
		str.Errno = 42014 // 获取参数失败
		flog.Log.Errorf("error: %s payload or values is nil, result.Result:%+v", devSN, *payload)
		return
	}

	var values = make(map[string]interface{})

	for k, v := range payload.Values {
		kk, ok := def.SetTiming[k]
		if !ok {
			flog.Log.Errorf("error: %s unsupported Settings, key:%s", devSN, k)
			str.Errno = 42015
		}
		values[kk] = v
	}

	resp := hub.SetData(devSN, timingBlock, values)
	if resp.Errno != 0 {
		flog.Log.Errorf("error: %s hub.SetData fail, resp.Errno:%d", devSN, resp.Errno)
		str.Errno = resp.Errno
	}

	publish(c, fmt.Sprintf(remoteSetTimingRespTopic, "set", devSN), str)
}

type getTimingArgs struct {
	ID uint32 `json:"id"`
}

type getTimingResp struct {
	Id     uint32        `json:"id"`
	Errno  int           `json:"errno"`
	Result *getInnerData `json:"result"`
}

type getInnerData struct {
	SN     string    `json:"sn"`
	Values *timePara `json:"values"`
}

type timePara struct {
	Year   string `json:"year"`
	Month  string `json:"month"`
	Day    string `json:"day"`
	Hour   string `json:"hour"`
	Minute string `json:"minute"`
	Second string `json:"second"`
}

func handleGetTiming(c mqtt.Client, devSN string, payload *getTimingArgs) {
	//下发读取并返回推送消息
	var gtr = &getTimingResp{
		Id:    payload.ID,
		Errno: 0,
		Result: &getInnerData{
			SN:     devSN,
			Values: nil,
		},
	}
	defer publish(c, fmt.Sprintf(remoteSetTimingRespTopic, "get", devSN), gtr)

	if payload == nil {
		gtr.Errno = 42014 // 获取参数失败
		flog.Log.Errorf("error: %s payload is nil, result.Result:%+v", devSN, *payload)
		return
	}

	//获取数据
	resp := hub.GetData(devSN, timingBlock, 1)
	if resp.Errno != 0 {
		gtr.Errno = resp.Errno
		flog.Log.Errorf("error: %s get %s data fail,errno: %d", devSN, timingBlock, resp.Errno)
		return
	}

	if resp.Result.Values == nil {
		gtr.Errno = 42014 // 获取参数失败
		flog.Log.Errorf("error: %s resp.Result.Values is nil, result.Result:%v", devSN, resp.Result)
		return
	}

	gtr.Result.Values, gtr.Errno = handleTimeKV(resp.Result.Values)
	return
}

func handleTimeKV(kv map[string]interface{}) (tp *timePara, errno int) {
	year, ok := kv[def.SystemYear].(string)
	if !ok {
		flog.Log.Errorf("error: has no %s, getResp.Result.Values:%v", def.SystemYear, kv)
		errno = 42014
		return
	}

	month, ok := kv[def.SystemMonth].(string)
	if !ok {
		flog.Log.Errorf("error: has no %s, getResp.Result.Values:%v", def.SystemMonth, kv)
		errno = 42014
		return
	}

	day, ok := kv[def.SystemDay].(string)
	if !ok {
		flog.Log.Errorf("error: has no %s, getResp.Result.Values:%v", def.SystemDay, kv)
		errno = 42014
		return
	}

	hour, ok := kv[def.SystemHour].(string)
	if !ok {
		flog.Log.Errorf("error: has no %s, getResp.Result.Values:%v", def.SystemHour, kv)
		errno = 42014
		return
	}

	minute, ok := kv[def.SystemMinute].(string)
	if !ok {
		flog.Log.Errorf("error: has no %s, getResp.Result.Values:%v", def.SystemMinute, kv)
		errno = 42014
		return
	}

	second, ok := kv[def.SystemSecond].(string)
	if !ok {
		flog.Log.Errorf("error: has no %s, getResp.Result.Values:%v", def.SystemSecond, kv)
		errno = 42014
		return
	}

	tp = &timePara{
		Year:   year,
		Month:  month,
		Day:    day,
		Hour:   hour,
		Minute: minute,
		Second: second,
	}
	return tp, 0
}
