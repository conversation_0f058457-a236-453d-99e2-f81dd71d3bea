package setting

import (
	"encoding/json"
	"foxess.cloud/toolbox/flog"
	"foxess.cockscomb/def"
	mqtt "github.com/eclipse/paho.mqtt.golang"
	"strconv"
	"time"
)

func publish(c mqtt.Client, topic string, value interface{}) {
	var qos byte
	if len([]byte(def.QOS)) == 0 {
		qos = 0x00
	}
	qosInt, _ := strconv.Atoi(def.QOS)
	qos = byte(qosInt)
	if qos != 0x00 && qos != 0x01 && qos != 0x02 {
		qos = 0x00
	}

	p, err := json.<PERSON>(value)
	if err != nil {
		flog.Log.<PERSON>("error: json marshal fail:", err)
	}
	tk := c.<PERSON>(topic, qos, false, p)
	go func() {
		if !tk.WaitTimeout(5 * time.Second) {
			flog.Log.<PERSON>("published timeout")
			return
		}
		flog.Log.Infof("published %s qos:%v success:%s", topic, qos, string(p))
	}()
}
