package setting

import (
	"encoding/json"
	"fmt"
	"foxess.cloud/toolbox/flog"
	"foxess.cockscomb/app/service/hub"
	mqtt "github.com/eclipse/paho.mqtt.golang"
	"strings"
	"time"
)

const (
	// 过欠压保护参数获取
	getDataRequestTopic  = `foxess/device/+/get/v0/+/req`
	getDataResponseTopic = `foxess/device/%s/get/v0/%s/resp`
)

const (
	gridVolt          = `gridVolt`
	gridFreq          = `gridFreq`
	startupParameters = `startupParameters`
)

type getDataArgs struct {
	ID uint32 `json:"id"`
}

type getDataResponse struct {
	ID     uint32 `json:"id"`
	Errno  int    `json:"errno"`
	Result *Data  `json:"result,omitempty"`
}

type Data struct {
	SN     string            `json:"sn"`
	Values map[string]string `json:"values"`
}

func GetData(c mqtt.Client) {
	token := c.Subscribe(getDataRequestTopic, 0, handleGetDataRequest)
	go func() {
		if !token.WaitTimeout(5 * time.Second) {
			flog.Log.Errorf("subscribe timeout 5s")
			return
		}
	}()
}

func handleGetDataRequest(c mqtt.Client, msg mqtt.Message) {
	resource := msg.Topic()
	payload := msg.Payload()

	flog.Log.Infof("subscribe %s success:%s", resource, string(payload))

	rscs := strings.Split(resource, "/")
	if len(rscs) != 7 {
		flog.Log.Errorln("invalid topic:", resource)
		return
	}

	topicName := rscs[2]
	devSN := rscs[5]

	var args *getDataArgs
	err := json.Unmarshal(payload, &args)
	if err != nil {
		flog.Log.Errorf("error: %s json.Unmarshal fail, payload:%s", devSN, string(payload))
		return
	}

	var response *getDataResponse
	switch topicName {
	case gridVolt:
		response = handleGetData(devSN, topicName, args.ID)
	case gridFreq:
		response = handleGetData(devSN, topicName, args.ID)
	case startupParameters:
		response = handleGetData(devSN, topicName, args.ID)
	default:
		response = &getDataResponse{
			ID:    args.ID,
			Errno: 42014, // 获取参数失败
		}
	}

	publish(c, fmt.Sprintf(getDataResponseTopic, topicName, devSN), response)
}

func handleGetData(devSN, key string, id uint32) (response *getDataResponse) {
	key = strings.ToLower(key)
	response = &getDataResponse{
		ID: id,
	}
	resp := hub.GetData(devSN, key, 1)
	if resp.Errno != 0 {
		response.Errno = resp.Errno
		flog.Log.Errorf("error: %s get %s data fail,errno: %d", devSN, key, resp.Errno)
		return
	}
	if resp.Result.Values == nil {
		response.Errno = 42014 // 获取参数失败
		flog.Log.Errorf("error: %s resp.Result.Values is nil, result.Result:%v", devSN, resp.Result)
		return
	}

	// 创建一个新的 map[string]string
	stringMap := make(map[string]string)
	for enableMapKey, _ := range enableMapKeys {
		settingKey := fmt.Sprintf("%s__%s", key, strings.ToLower(enableMapKey))
		value, ok := resp.Result.Values[settingKey]
		if ok {
			stringMap[enableMapKey] = fmt.Sprintf("%v", value)
		}
	}
	response.Result = &Data{
		SN:     devSN,
		Values: stringMap,
	}
	return
}

var enableMapKeys = map[string]struct{}{
	"OVP1Value":                     {},
	"OVP1Time":                      {},
	"OVP2Value":                     {},
	"OVP2Time":                      {},
	"UVP1Value":                     {},
	"UVP1Time":                      {},
	"UVP2Value":                     {},
	"UVP2Time":                      {},
	"OFP1Value":                     {},
	"OFP1Time":                      {},
	"OFP2Value":                     {},
	"OFP2Time":                      {},
	"UFP1Value":                     {},
	"UFP1Time":                      {},
	"UFP2Value":                     {},
	"UFP2Time":                      {},
	"ConnectionUpperFrequencyValue": {},
	"ConnectionLowerFrequencyValue": {},
	"ReconnectionObservationTime":   {},
}
