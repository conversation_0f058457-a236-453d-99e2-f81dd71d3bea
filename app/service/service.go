package service

import (
	"crypto/tls"
	"errors"
	"fmt"
	"foxess.cloud/toolbox/flog"
	"github.com/eclipse/paho.mqtt.golang"
	"log"
	"time"
)

var OnConnectHandlerFunc func(client mqtt.Client)

func InitMQTTClient(rootCAPath, certPAth, privKeyPath, deviceID, broker, user, password string) (cli mqtt.Client, err error) {

	opts := newOpts(deviceID, user, password)
	opts.SetOnConnectHandler(OnConnectHandlerFunc)
	opts.SetConnectionLostHandler(func(c mqtt.Client, err error) {
		flog.Log.Infof("disconnect on %s lost handler %v", broker, err)
	})
	opts.SetProtocolVersion(4)
	opts.SetPingTimeout(300 * time.Hour)
	opts.SetTLSConfig(loadCerts(rootCAPath, certPAth, privKeyPath))
	opts.AddBroker("ssl://" + broker)
	cli = mqtt.NewClient(opts)
	t := cli.Connect()

	if err = t.Error(); err != nil || !t.WaitTimeout(10*time.Second) {
		flog.Log.Errorln("connect timeout or failed:", err)
		err = errors.New(fmt.Sprintf("connect timeout or failed:%s", err))
		return
	}
	flog.Log.Infoln("connect broker:", broker, "success!")
	return
}

func loadCerts(rootCAPath, certPAth, privKeyPath string) *tls.Config {
	//var certpool *x509.CertPool
	//if len(rootCAPath) > 0 {
	//	pemCerts, err := os.ReadFile(rootCAPath)
	//	if err != nil {
	//		log.Fatal(err)
	//	}
	//
	//	certpool = x509.NewCertPool()
	//	if !certpool.AppendCertsFromPEM(pemCerts) {
	//		log.Fatal(err)
	//	}
	//}

	// Import client certificate/key pair
	cert, err := tls.LoadX509KeyPair(certPAth, privKeyPath)
	if err != nil {
		log.Fatal(err)
	}

	return &tls.Config{
		//RootCAs:      certpool,
		//ClientAuth:   tls.RequireAndVerifyClientCert,
		Certificates:       []tls.Certificate{cert},
		InsecureSkipVerify: true,
		//MinVersion:   tls.VersionTLS12,
	}
}

// newOpts 创建一个mqtt配置
func newOpts(clientID, userName, password string) *mqtt.ClientOptions {
	opts := mqtt.NewClientOptions()
	opts.SetConnectTimeout(15 * time.Second)
	opts.SetKeepAlive(30 * time.Second)
	opts.SetCleanSession(true)
	opts.SetMaxReconnectInterval(10 * time.Second)
	opts.SetUsername(userName)
	opts.SetPassword(password)
	opts.SetClientID(clientID)
	return opts
}
