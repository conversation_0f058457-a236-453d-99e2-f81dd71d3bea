package hub

import (
	"encoding/json"
	"fmt"
	"foxess.cloud/toolbox/flog"
	"foxess.cockscomb/def"
	"foxess.cockscomb/model/redis"
)

func GetGroup(group, devSN, modType string) string {
	indexStr := "0"
	switch def.HubGroup {
	case "hub":
		if !def.HUB_CLUSTER_ENABLE {
			return group
		}
		switch modType {
		case "dev":
			indexStr = redis.GetDevHubIndex(devSN)
		case "mod":
			indexStr = redis.GetModHubIndex(devSN)
		}
		return def.HubGroup + "-" + indexStr
	default:
		return group
	}
}

// GetData 获取数据
func GetData(devSN, key string, hasVersionHead int) (resp *GetResp) {
	args := map[string]interface{}{"key": key, "hasVersionHead": hasVersionHead}

	msg := def.Micbus.RequestGroupAll(GetGroup(def.HubGroup, devSN, "dev"), fmt.Sprintf(def.HubGetResource, devSN), "", def.ToJsonData(args), 30)
	if msg != nil {
		e := json.Unmarshal(msg.GetPayload(), &resp)
		if e != nil {
			flog.Log.Errorln(devSN, "json.Unmarshal err:", e)
			resp.Errno = 41208
			return
		}
		return
	}
	if resp == nil {
		resp = &GetResp{Errno: 41203}
	}

	return
}

// GetResp get返回值
type GetResp struct {
	Errno  int    `json:"errno"`
	Result *Value `json:"result"`
}

type Value struct {
	SN        string                 `json:"sn"`
	Version   string                 `json:"version"`
	Key       string                 `json:"key"`
	Values    map[string]interface{} `json:"values"`
	RawValues string                 `json:"rawValues"`
}

// SetData 下发设置
func SetData(devSN, key string, values map[string]interface{}) (resp *SetResp) {
	args := SetReq{
		SN:     devSN,
		Key:    key,
		Values: values,
	}

	msg := def.Micbus.RequestGroupAll(GetGroup(def.HubGroup, devSN, "dev"), fmt.Sprintf(def.HubSetResource, devSN), "", def.ToJsonData(args), 30)
	if msg != nil {
		e := json.Unmarshal(msg.GetPayload(), &resp)
		if e != nil {
			flog.Log.Errorln(devSN, "json.Unmarshal err:", e)
			resp.Errno = 41208
			return
		}
		return
	}
	if resp == nil {
		resp = &SetResp{Errno: 41203}
	}

	return
}

type SetReq struct {
	SN     string                 `json:"sn"`
	Key    string                 `json:"key"`
	Values map[string]interface{} `json:"values"`
}

// SetResp set返回值
type SetResp struct {
	Errno  int         `json:"errno"`
	Result interface{} `json:"result"`
}

// GetUI 从 hub 获取 ui
func GetUI(devSN, lang string) (resp *UIResp) {
	args := map[string]interface{}{
		"lang":   lang,
		"access": 255,
		"sn":     devSN}

	msg := def.Micbus.RequestGroup(GetGroup(def.HubGroup, devSN, "dev"), fmt.Sprintf(def.HubUIResource, devSN), "", def.ToJsonData(args), 30)
	if msg != nil {
		e := json.Unmarshal(msg.GetPayload(), &resp)
		if e != nil {
			flog.Log.Errorln(devSN, "json.Unmarshal err:", e)
			resp.Errno = 41208
			return
		}
		return
	}
	resp.Errno = 41203 //超时
	return
}

// UIResp getUI 返回值
type UIResp struct {
	Errno  int    `json:"errno"`
	Result UIDesc `json:"result"`
}

// ElemType UI元素类型描述
type ElemType struct {
	ValueType string      `json:"valueType"` //值类型
	UIType    string      `json:"uiType"`    //UI类型
	UIItems   interface{} `json:"uiItems"`   //ui的选项
}

// Range1 定义域
type Range1 struct {
	Enable bool    `json:"enable" yaml:"enable"`
	Lo     float64 `json:"lo" yaml:"lo"`
	Hi     float64 `json:"hi" yaml:"hi"`
}

// UIProp UI属性描述
type UIProp struct {
	Name     string    `json:"name"`
	Key      string    `json:"key"`
	ElemType *ElemType `json:"elemType"`
	Range    *Range1   `json:"range"`
	Unit     string    `json:"unit"`
	Level    int       `json:"level"`
}

// UIPara UI参数区描述
type UIPara struct {
	Name       string    `json:"name"`
	Key        string    `json:"key"`
	Block      bool      `json:"block"`
	Tips       string    `json:"tips"`
	Type       string    `json:"type"`
	Disable    bool      `json:"disable"`
	Properties []*UIProp `json:"properties"`
}

// UIDesc UI描述
type UIDesc struct {
	UIVersion string    `json:"version"`
	UIParas   []*UIPara `json:"parameters"`
}
