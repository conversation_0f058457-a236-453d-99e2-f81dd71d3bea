package app

import (
	"encoding/binary"
	"encoding/json"
	"fmt"
	microbus "foxess.cloud/fmicrobus"
	"foxess.cloud/toolbox/flog"
	"foxess.cockscomb/app/service"
	"foxess.cockscomb/app/service/setting"
	"foxess.cockscomb/def"
	mqtt "github.com/eclipse/paho.mqtt.golang"
	"log"
	"strconv"
	"strings"
	"time"
)

const (
	topicRealData = "foxess/device/realData/v0/%s/json"
)

const (
	GridTieInverter       = "GTI" //并网机
	EnergyStorageInverter = "ESI" //储能机
)

func Run() {
	service.OnConnectHandlerFunc = func(client mqtt.Client) {
		//数据流处理
		go dataFlow(client)

		//校时
		go setting.SetTiming(client)

		// 获取数据
		go setting.GetData(client)

		flog.Log.Infof("connect %s success", def.MQTT_BROKERS)
	}

	//初始化 MQTT 客户端
	_, err := service.InitMQTTClient(def.MQTT_ROOT_CA, def.MQTT_CERT, def.MQTT_PRIV_KEY, def.MQTT_CLIENTID, def.MQTT_BROKERS, def.MQTT_USER, def.MQTT_PASSWORD)
	if err != nil {
		flog.Log.Fatalln("fatal: init mqtt client failed:", err)
	}
}

type Data struct {
	SN         string               `json:"sn"`         //设备SN
	ModuleSN   string               `json:"moduleSN"`   //模块sn
	Properties map[string]*Property `json:"properties"` //属性值
	Info       *Info                `json:"info"`       //设备信息
	ExInfo     *ExInfo              `json:"exInfo"`     //对外版本

}

// ExInfo 对外版本
type ExInfo struct {
	MasterVersion  string
	SlaveVersion   string
	ManagerVersion string
	AFCIVersion    string
}

// Info 新版本的设备属性信息
type Info struct {
	MasterVersion  string  `json:"masterVersion"`  //主CPU版本
	SlaveVersion   string  `json:"slaveVersion"`   //副CPU版本
	ManagerVersion string  `json:"managerVersion"` //HMI主CPU版本
	AFCIVersion    string  `json:"afciVersion"`    //AFCI版本
	DeviceFactory  uint16  `json:"deviceFactory"`  //厂商 0麦田温州 1麦田无锡
	ProductType    string  `json:"productType"`    //设备产品系列
	DeviceType     string  `json:"deviceType"`     //设备机型
	Capacity       float64 `json:"capacity"`       //设备容量
}

type Property struct {
	Value     interface{} `json:"value"`
	Timestamp int64       `json:"timestamp"`
	Unit      string      `json:"unit"`
}

func getPropertyValue(value *Property, dataType string) interface{} {
	if value != nil {
		return value.Value
	}
	switch dataType {
	case "string":
		return ""
	case "int":
		return 0
	case "int64":
		return int64(0)
	case "float64":
		return float64(0)
	}
	return ""
}

type FaultMap map[int]uint32

const (
	HBIT = uint32(0x80000000)
)

// 与原始帧进行对比
func (f1 FaultMap) compare(f0 FaultMap) (alms []int, count int) {
	if len(f0) == 0 { //则认为上一次的故障字全部是0
		f0 = make(FaultMap)
	}
	if len(f1) == 0 { //本次帧为0
		f1 = make(FaultMap)
	}
	alms = make([]int, 0)
	//比较帧
	for k1, v1 := range f1 { //遍历后帧
		for i := 0; i < 32; i++ {
			a := HBIT >> i
			if v1&a == a {
				count++
			}
		}

		if v0, ok := f0[k1]; ok { //前帧有数值
			if v0 == v1 { //未发生变化
				continue
			} else { //发生过变化
				for i := 0; i < 32; i++ {
					n := HBIT >> i
					s1 := v1 & n
					s0 := v0 & n
					c := (k1-1)*32 + i + 1
					if s1 > s0 { //发生报警
						alms = append(alms, c)
					} else if s0 > s1 { //报警恢复
						alms = append(alms, -c)
					}
				}
			}
		} else {
			for i := 0; i < 32; i++ {
				n := HBIT >> i
				s1 := v1 & n
				c := (k1-1)*32 + i + 1
				if s1 > 0 {
					alms = append(alms, c)
				}
			}
		}
	} //
	for k0, v0 := range f0 { //遍历前帧
		if _, ok := f1[k0]; !ok { //如果前帧有后帧没有,按恢复处理
			for i := 0; i < 32; i++ {
				n := HBIT >> i
				s0 := v0 & n
				c := (k0-1)*32 + i + 1
				if s0 > 0 {
					alms = append(alms, -c)
				}
			}
		}
	}
	return
}

func HandleRealdata(client mqtt.Client, devSN, deviceType string, payload []byte, timestamp int64) {
	var qos byte
	if len([]byte(def.QOS)) == 0 {
		qos = 0x00
	}
	qosInt, _ := strconv.Atoi(def.QOS)
	qos = byte(qosInt)
	if qos != 0x00 && qos != 0x01 && qos != 0x02 {
		qos = 0x00
	}

	var data Data
	e := json.Unmarshal(payload, &data)
	if e != nil {
		log.Println("parse payload error:", e)
		return
	}

	var invModel string
	if _, ok := data.Properties["SoC"]; !ok {
		invModel = GridTieInverter
	} else {
		invModel = EnergyStorageInverter
	}

	master, slave, manager := getVersion(data)

	res := map[string]interface{}{
		"invSN":         data.SN,
		"dlSN":          data.ModuleSN,
		"invModel":      invModel,
		"timestamp":     timestamp,
		"masterVersion": master,
		"slaveVersion":  slave,
		"HMIVersion":    manager,
		"deviceType":    getPropertyValue(data.Properties["deviceType"], "string"),
		"ratedPower":    getPropertyValue(data.Properties["capacity"], "string"),

		"state":           getPropertyValue(data.Properties["masterState"], "int"),
		"gridPower":       getPropertyValue(data.Properties["meterPower"], "float64"),
		"solarPower":      getPropertyValue(data.Properties["pvPower"], "float64"),
		"batPower":        getPropertyValue(data.Properties["invBatPower"], "float64"),
		"loadPower":       getPropertyValue(data.Properties["loadsPower"], "float64"),
		"solarTotal":      getPropertyValue(data.Properties["generation"], "float64"),
		"todayYield":      getPropertyValue(data.Properties["todayYield"], "float64"),
		"gridConsumption": getPropertyValue(data.Properties["gridConsumption"], "float64"),
		"feedin":          getPropertyValue(data.Properties["feedin"], "float64"),
		"loads":           getPropertyValue(data.Properties["loads"], "float64"),
		"input":           getPropertyValue(data.Properties["input"], "float64"),

		"RVolt":    getPropertyValue(data.Properties["RVolt"], "float64"),
		"RCurrent": getPropertyValue(data.Properties["RCurrent"], "float64"),
		"RFreq":    getPropertyValue(data.Properties["RFreq"], "float64"),
		"RPower":   getPropertyValue(data.Properties["RPower"], "float64"),

		"SVolt":    getPropertyValue(data.Properties["SVolt"], "float64"),
		"SCurrent": getPropertyValue(data.Properties["SCurrent"], "float64"),
		"SFreq":    getPropertyValue(data.Properties["SFreq"], "float64"),
		"SPower":   getPropertyValue(data.Properties["SPower"], "float64"),

		"TVolt":    getPropertyValue(data.Properties["TVolt"], "float64"),
		"TCurrent": getPropertyValue(data.Properties["TCurrent"], "float64"),
		"TFreq":    getPropertyValue(data.Properties["TFreq"], "float64"),
		"TPower":   getPropertyValue(data.Properties["TPower"], "float64"),

		"pv1Volt":      getPropertyValue(data.Properties["pv1Volt"], "float64"),
		"pv1Current":   getPropertyValue(data.Properties["pv1Current"], "float64"),
		"pv1Power":     getPropertyValue(data.Properties["pv1Power"], "float64"),
		"pv2Volt":      getPropertyValue(data.Properties["pv2Volt"], "float64"),
		"pv2Current":   getPropertyValue(data.Properties["pv2Current"], "float64"),
		"pv2Power":     getPropertyValue(data.Properties["pv2Power"], "float64"),
		"pv3Volt":      getPropertyValue(data.Properties["pv3Volt"], "float64"),
		"pv3Current":   getPropertyValue(data.Properties["pv3Current"], "float64"),
		"pv3Power":     getPropertyValue(data.Properties["pv3Power"], "float64"),
		"pv4Volt":      getPropertyValue(data.Properties["pv4Volt"], "float64"),
		"pv4Current":   getPropertyValue(data.Properties["pv4Current"], "float64"),
		"pv4Power":     getPropertyValue(data.Properties["pv4Power"], "float64"),
		"gridRVolt":    getPropertyValue(data.Properties["RVolt"], "float64"),
		"gridRCurrent": getPropertyValue(data.Properties["RCurrent"], "float64"),
		"gridRPower":   getPropertyValue(data.Properties["RPower"], "float64"),
		"gridSVolt":    getPropertyValue(data.Properties["SVolt"], "float64"),
		"gridSCurrent": getPropertyValue(data.Properties["SCurrent"], "float64"),
		"gridSPower":   getPropertyValue(data.Properties["SPower"], "float64"),
		"gridTVolt":    getPropertyValue(data.Properties["TVolt"], "float64"),
		"gridTCurrent": getPropertyValue(data.Properties["TCurrent"], "float64"),
		"gridTPower":   getPropertyValue(data.Properties["TPower"], "float64"),
		"temperature":  getPropertyValue(data.Properties["invTemperation"], "float64"),
		"epsRVolt":     getPropertyValue(data.Properties["epsVoltR"], "float64"),
		"epsRCurrent":  getPropertyValue(data.Properties["epsCurrentR"], "float64"),
		"epsRPower":    getPropertyValue(data.Properties["epsPowerR"], "float64"),
		"epsSVolt":     getPropertyValue(data.Properties["epsVoltS"], "float64"),
		"epsSCurrent":  getPropertyValue(data.Properties["epsCurrentS"], "float64"),
		"epsSPower":    getPropertyValue(data.Properties["epsPowerS"], "float64"),
		"epsTVolt":     getPropertyValue(data.Properties["epsVoltT"], "float64"),
		"epsTCurrent":  getPropertyValue(data.Properties["epsCurrentT"], "float64"),
		"epsTPower":    getPropertyValue(data.Properties["epsPowerT"], "float64"),
	}

	if invModel == EnergyStorageInverter {
		res["batDischargeTotal"] = getPropertyValue(data.Properties["dischargeEnergyToTal"], "float64")
		res["batChargeTotal"] = getPropertyValue(data.Properties["chargeEnergyToTal"], "float64")
		res["soc"] = getPropertyValue(data.Properties["SoC"], "float64")
		res["batCurrent"] = getPropertyValue(data.Properties["invBatCurrent"], "float64")
		res["batVolt"] = getPropertyValue(data.Properties["invBatVolt"], "float64")
	}

	i := 0
	oldFaultMap := make(FaultMap)
	newFaultMap := make(FaultMap)
	for i < 8 {
		buf := make([]byte, 4)
		num := uint32(getPropertyValue(data.Properties["fault"+strconv.Itoa(i+1)], "float64").(float64))
		binary.LittleEndian.PutUint32(buf, num)
		newFaultMap[i+1] = binary.BigEndian.Uint32(buf)
		i++
	}
	faults, _ := newFaultMap.compare(oldFaultMap)
	res["faults"] = faults
	p, err := json.Marshal(res)
	if err != nil {
		log.Println("json marshal error:", e)
	}
	tk := client.Publish(fmt.Sprintf(topicRealData, devSN), qos, false, p)
	go func() {
		if !tk.WaitTimeout(3 * time.Second) {
			flog.Log.Errorf("published timeout")
			return
		}
		flog.Log.Infof("published %s qos:%v success:%s", fmt.Sprintf(topicRealData, devSN), qos, string(p))
	}()
}

func dataFlow(client mqtt.Client) {
	def.Micbus.WaitAsync("$system/realdata/ledvance/+/type/+/json", func(mmsg *microbus.MicroMessage) {
		rsc := mmsg.GetResource()
		ss := strings.Split(rsc, "/")
		if len(ss) != 7 {
			flog.Log.Errorln("invalid resource:", rsc)
			return
		}
		switch ss[1] {
		case "realdata": //实时数据处理
			HandleRealdata(client, ss[3], ss[5], mmsg.GetPayload(), mmsg.GetTimestamp())
		default:
			flog.Log.Errorln("invalid resource:", rsc)
		}
	}, 100)
}

func getVersion(data Data) (master, slave, manager string) {
	if data.ExInfo == nil || *data.ExInfo == (ExInfo{}) {
		master = data.Info.MasterVersion
		slave = data.Info.SlaveVersion
		manager = data.Info.ManagerVersion
	} else if data.Info != nil {
		master = data.ExInfo.MasterVersion
		slave = data.ExInfo.SlaveVersion
		manager = data.ExInfo.ManagerVersion
	}
	return
}
