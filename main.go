package main

import (
	"foxess.cloud/toolbox/flog"
	"foxess.cockscomb/app"
	"foxess.cockscomb/model"
	"os"
	"os/signal"
	"syscall"
)

func main() {
	//日志初始化
	flog.InitLog()
	flog.Log.Infoln("init log over...")

	flog.Log.Infoln("start init db")
	model.Init()

	flog.Log.Infoln("start process...")
	app.Run()

	quit := make(chan os.Signal)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM, syscall.SIGQUIT)
	for s := range quit {
		switch s {
		case syscall.SIGINT, syscall.SIGTERM, syscall.SIGQUIT:
			flog.Log.Infoln("stop process and exiting...")
			os.Exit(0)
		}
	}
}
